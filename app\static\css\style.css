/* Custom styles for Retail POS System */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.table {
    border-radius: 10px;
    overflow: hidden;
}

.alert {
    border-radius: 8px;
    border: none;
}

/* POS specific styles */
.pos-container {
    height: calc(100vh - 120px);
}

.product-grid {
    max-height: 400px;
    overflow-y: auto;
}

.cart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    height: 100%;
}

.cart-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.total-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pos-container {
        height: auto;
    }
    
    .navbar-nav .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom badge colors */
.badge.bg-low-stock {
    background-color: #dc3545 !important;
}

.badge.bg-in-stock {
    background-color: #28a745 !important;
}

/* Form enhancements */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

/* Dashboard cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

/* Barcode scanner styles */
.barcode-scanner {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
}

.barcode-scanner.active {
    border-color: #28a745;
    background: #d4edda;
}

/* Receipt styles */
.receipt {
    max-width: 300px;
    margin: 0 auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.receipt-header {
    text-align: center;
    border-bottom: 1px dashed #000;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.receipt-total {
    border-top: 1px dashed #000;
    padding-top: 10px;
    margin-top: 10px;
    font-weight: bold;
}

/* Arabic RTL support */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl .navbar-nav {
    flex-direction: row-reverse;
}

.rtl .dropdown-menu {
    right: 0;
    left: auto;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
