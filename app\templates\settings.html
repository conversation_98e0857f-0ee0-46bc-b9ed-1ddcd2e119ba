{% extends "base.html" %}

{% block title %}Settings - Retail POS System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cog"></i> System Settings
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-store"></i> Shop Information</h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="shop_name" class="form-label">Shop Name</label>
                        <input type="text" class="form-control" id="shop_name" value="My Retail Store">
                    </div>
                    
                    <div class="mb-3">
                        <label for="shop_address" class="form-label">Address</label>
                        <textarea class="form-control" id="shop_address" rows="3">123 Main St, City, State 12345</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="shop_phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="shop_phone" value="******-0199">
                    </div>
                    
                    <div class="mb-3">
                        <label for="shop_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="shop_email" value="<EMAIL>">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Shop Info
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> Business Settings</h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="tax_rate" class="form-label">Default Tax Rate (%)</label>
                        <input type="number" class="form-control" id="tax_rate" value="15" step="0.01" min="0" max="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="currency_symbol" class="form-label">Currency Symbol</label>
                        <input type="text" class="form-control" id="currency_symbol" value="$" maxlength="3">
                    </div>
                    
                    <div class="mb-3">
                        <label for="low_stock_threshold" class="form-label">Low Stock Threshold</label>
                        <input type="number" class="form-control" id="low_stock_threshold" value="10" min="1">
                    </div>
                    
                    <div class="mb-3">
                        <label for="receipt_footer" class="form-label">Receipt Footer Message</label>
                        <textarea class="form-control" id="receipt_footer" rows="2">Thank you for your business!</textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> User Management</h5>
            </div>
            <div class="card-body">
                <p>Manage system users and their permissions.</p>
                <a href="{{ url_for('auth.register') }}" class="btn btn-success">
                    <i class="fas fa-user-plus"></i> Add New User
                </a>
                <button class="btn btn-info" onclick="alert('User list feature coming soon!')">
                    <i class="fas fa-list"></i> View All Users
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-database"></i> Database Management</h5>
            </div>
            <div class="card-body">
                <p>Backup and restore your database.</p>
                <button class="btn btn-warning" onclick="alert('Backup feature coming soon!')">
                    <i class="fas fa-download"></i> Create Backup
                </button>
                <button class="btn btn-danger" onclick="alert('Restore feature coming soon!')">
                    <i class="fas fa-upload"></i> Restore Database
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
