<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Retail POS System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block head %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-cash-register"></i> Retail POS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    
                    {% if current_user.has_permission('make_sales') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('sales.pos') }}">
                            <i class="fas fa-shopping-cart"></i> POS
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('manage_products') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i> Inventory
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('inventory.products') }}">Products</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.categories') }}">Categories</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.low_stock') }}">Low Stock</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('manage_purchases') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i> Purchases
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('purchases.index') }}">Purchase Orders</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('purchases.suppliers') }}">Suppliers</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('view_reports') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('reports.sales_report') }}">Sales Report</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports.profit_loss') }}">P&L Statement</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('sales.history') }}">Sales History</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            {% if current_user.has_permission('manage_users') %}
                            <li><a class="dropdown-item" href="{{ url_for('main.settings') }}">Settings</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.register') }}">Add User</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
