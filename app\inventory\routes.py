from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.inventory import bp
from app.models import Product, Category, Supplier
from app import db

@bp.route('/')
@login_required
def index():
    if not current_user.has_permission('manage_products'):
        flash('You do not have permission to access inventory.', 'error')
        return redirect(url_for('main.dashboard'))
    
    products = Product.query.all()
    return render_template('inventory/index.html', products=products)

@bp.route('/products')
@login_required
def products():
    if not current_user.has_permission('manage_products'):
        flash('You do not have permission to access products.', 'error')
        return redirect(url_for('main.dashboard'))
    
    products = Product.query.all()
    categories = Category.query.all()
    return render_template('inventory/products.html', products=products, categories=categories)

@bp.route('/categories')
@login_required
def categories():
    if not current_user.has_permission('manage_products'):
        flash('You do not have permission to access categories.', 'error')
        return redirect(url_for('main.dashboard'))
    
    categories = Category.query.all()
    return render_template('inventory/categories.html', categories=categories)

@bp.route('/low-stock')
@login_required
def low_stock():
    low_stock_products = Product.query.filter(Product.quantity <= Product.min_quantity).all()
    return render_template('inventory/low_stock.html', products=low_stock_products)
