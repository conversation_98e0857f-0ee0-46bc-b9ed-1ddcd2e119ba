# 🛒 Retail POS System

A comprehensive, web-based Point of Sale (POS) system designed for small retail shops. Built with Python Flask and SQLite, this system provides all essential retail operations in a modern, user-friendly interface.

## ✨ Features

### 🔸 Sales Management
- **Barcode Scanning**: Scan products using barcode scanner
- **Receipt Generation**: Create and print customizable receipts with shop details
- **Payment Processing**: Support for cash, card, and mixed payments
- **Discounts & Taxes**: Apply discounts and taxes with flexible rates
- **Sales History**: Complete sales history with filtering options

### 🔸 Inventory Management
- **Product Management**: Add, edit, delete products with detailed information
- **Category Organization**: Organize products by categories
- **Stock Monitoring**: Auto-detect and notify for low-stock items
- **Bulk Operations**: Import/export products via Excel/CSV
- **Barcode Integration**: Generate and manage product barcodes

### 🔸 Purchase & Supplier Management
- **Supplier Database**: Maintain supplier information and contacts
- **Purchase Orders**: Create and manage purchase invoices
- **Automatic Updates**: Auto-update inventory quantities from purchases
- **Document Management**: Attach scanned invoices (PDF/images)

### 🔸 Dashboard & Analytics
- **Real-time Metrics**: Daily and monthly sales statistics
- **Performance Tracking**: Top-selling items and profit margins
- **Inventory Alerts**: Low-stock notifications and alerts
- **Quick Actions**: Fast access to common operations

### 🔸 User Management
- **Role-based Access**: Admin, Supervisor, and Cashier roles
- **Permission Control**: Granular permissions for different operations
- **Activity Tracking**: Monitor user activities and sales
- **Account Management**: Enable/disable user accounts

### 🔸 Comprehensive Reporting
- **Profit & Loss**: Complete P&L statements with expenses
- **Sales Reports**: Detailed sales analysis and trends
- **Inventory Reports**: Stock movement and valuation reports
- **Export Options**: PDF and Excel export for all reports

### 🔸 Additional Features
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Multi-language**: English and Arabic support with RTL
- **Backup & Restore**: Database backup and restoration
- **Expense Tracking**: Track business expenses for accurate P&L
- **Thermal Printer**: Optional thermal receipt printer integration

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd RetailSystem
   ```

2. **Run the automated setup**
   ```bash
   python setup.py
   ```

3. **Activate virtual environment**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

4. **Start the application**
   ```bash
   python app.py
   ```

5. **Access the system**
   Open your browser and go to: http://localhost:5000

### Manual Installation

If the automated setup doesn't work, follow these manual steps:

1. **Create virtual environment**
   ```bash
   python -m venv venv
   ```

2. **Activate virtual environment**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Initialize database**
   ```bash
   python init_db.py
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

## 🔐 Default Login Credentials

| Role | Username | Password |
|------|----------|----------|
| Administrator | admin | admin123 |
| Cashier | cashier | cashier123 |

**⚠️ Important: Change these passwords immediately in production!**

## 📁 Project Structure

```
RetailSystem/
├── app/                    # Main application package
│   ├── auth/              # Authentication routes
│   ├── inventory/         # Inventory management
│   ├── sales/             # Sales and POS
│   ├── purchases/         # Purchase management
│   ├── reports/           # Reporting system
│   ├── api/               # API endpoints
│   ├── static/            # CSS, JS, images
│   ├── templates/         # HTML templates
│   └── models.py          # Database models
├── uploads/               # File uploads
├── backups/               # Database backups
├── logs/                  # Application logs
├── app.py                 # Main application file
├── init_db.py            # Database initialization
├── setup.py              # Automated setup script
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🎯 User Roles & Permissions

### Administrator
- Full system access
- User management
- System settings
- All reports and analytics
- Database management

### Supervisor
- Product and inventory management
- Purchase management
- Sales operations
- Most reports (except user management)
- Expense tracking

### Cashier
- Sales operations (POS)
- Basic sales reports
- View inventory (read-only)

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///retail_pos.db
FLASK_ENV=development
FLASK_DEBUG=True
```

### System Settings
Access system settings through the web interface:
- Shop information (name, address, contact)
- Tax rates and currency
- Receipt customization
- Low stock thresholds
- User management

## 📊 Database Schema

The system uses SQLite with the following main tables:
- **users**: User accounts and roles
- **categories**: Product categories
- **products**: Product information and inventory
- **suppliers**: Supplier information
- **sales**: Sales transactions
- **sale_items**: Individual sale items
- **purchases**: Purchase orders
- **purchase_items**: Purchase order items
- **expenses**: Business expenses
- **system_settings**: Configuration settings

## 🔄 Backup & Restore

### Automatic Backups
- Daily automatic backups (configurable)
- Backup files stored in `backups/` directory
- Retention policy for old backups

### Manual Backup
```bash
python -c "from app.utils import backup_database; backup_database()"
```

### Restore
```bash
python -c "from app.utils import restore_database; restore_database('backup_file.db')"
```

## 🌐 Multi-language Support

The system supports English and Arabic:
- Interface language switching
- RTL (Right-to-Left) support for Arabic
- Localized date and number formats
- Translatable receipt templates

## 🖨️ Thermal Printer Integration

For thermal receipt printers:
1. Install printer drivers
2. Configure printer in system settings
3. Test print functionality
4. Customize receipt templates

## 🚀 Deployment

### Development
```bash
python app.py
```

### Production
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker (Optional)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the FAQ section
- Submit issues on GitHub
- Contact the development team

## 🔮 Roadmap

- [ ] Multi-branch support
- [ ] Advanced analytics and forecasting
- [ ] Mobile app for inventory management
- [ ] Integration with accounting software
- [ ] Cloud synchronization
- [ ] Advanced barcode generation
- [ ] Customer loyalty program
- [ ] Online ordering integration

---

**Built with ❤️ for small retail businesses**
