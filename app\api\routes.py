from flask import jsonify, request
from flask_login import login_required, current_user
from app.api import bp
from app.models import Product, Sale, SaleItem
from app import db

@bp.route('/products/search')
@login_required
def search_products():
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])
    
    products = Product.query.filter(
        Product.name.contains(query) | 
        Product.barcode.contains(query)
    ).filter(Product.is_active == True).limit(10).all()
    
    return jsonify([{
        'id': p.id,
        'name': p.name,
        'barcode': p.barcode,
        'price': float(p.selling_price),
        'quantity': p.quantity
    } for p in products])

@bp.route('/products/barcode/<barcode>')
@login_required
def get_product_by_barcode(barcode):
    product = Product.query.filter_by(barcode=barcode, is_active=True).first()
    if not product:
        return jsonify({'error': 'Product not found'}), 404
    
    return jsonify({
        'id': product.id,
        'name': product.name,
        'barcode': product.barcode,
        'price': float(product.selling_price),
        'quantity': product.quantity
    })
