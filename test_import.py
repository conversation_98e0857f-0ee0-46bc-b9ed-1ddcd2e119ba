#!/usr/bin/env python3
"""
Test script to check imports and identify issues
"""

try:
    print("Testing basic imports...")
    import sys
    print(f"Python version: {sys.version}")
    
    print("Testing Flask import...")
    import flask
    print(f"Flask version: {flask.__version__}")
    
    print("Testing other imports...")
    from flask import Flask
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager
    from flask_migrate import Migrate
    from flask_babel import Babel
    import os
    from dotenv import load_dotenv
    
    print("All imports successful!")
    
    print("Testing app creation...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db = SQLAlchemy()
    db.init_app(app)
    
    print("Basic app creation successful!")
    
except ImportError as e:
    print(f"Import error: {e}")
except Exception as e:
    print(f"Other error: {e}")
    import traceback
    traceback.print_exc()
