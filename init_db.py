#!/usr/bin/env python3
"""
Database initialization script for Retail POS System
This script creates the database tables and adds initial data
"""

from app import create_app, db
from app.models import User, Category, Product, Supplier, SystemSettings
from datetime import datetime
import os

def init_database():
    """Initialize the database with tables and sample data"""
    app = create_app()
    
    with app.app_context():
        # Create all tables
        print("Creating database tables...")
        db.create_all()
        
        # Check if admin user already exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Creating default admin user...")
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                role='admin'
            )
            admin_user.set_password('admin123')  # Change this in production!
            db.session.add(admin_user)
        
        # Create sample cashier user
        cashier_user = User.query.filter_by(username='cashier').first()
        if not cashier_user:
            print("Creating sample cashier user...")
            cashier_user = User(
                username='cashier',
                email='<EMAIL>',
                full_name='<PERSON> Cashier',
                role='cashier'
            )
            cashier_user.set_password('cashier123')
            db.session.add(cashier_user)
        
        # Create sample categories
        categories_data = [
            {'name': 'Electronics', 'description': 'Electronic devices and accessories'},
            {'name': 'Clothing', 'description': 'Apparel and fashion items'},
            {'name': 'Food & Beverages', 'description': 'Food items and drinks'},
            {'name': 'Books', 'description': 'Books and educational materials'},
            {'name': 'Home & Garden', 'description': 'Home improvement and garden supplies'},
            {'name': 'Health & Beauty', 'description': 'Health and beauty products'},
        ]
        
        for cat_data in categories_data:
            category = Category.query.filter_by(name=cat_data['name']).first()
            if not category:
                print(f"Creating category: {cat_data['name']}")
                category = Category(**cat_data)
                db.session.add(category)
        
        # Commit categories first so we can reference them
        db.session.commit()
        
        # Create sample supplier
        supplier = Supplier.query.filter_by(name='ABC Wholesale').first()
        if not supplier:
            print("Creating sample supplier...")
            supplier = Supplier(
                name='ABC Wholesale',
                contact_person='Jane Smith',
                phone='******-0123',
                email='<EMAIL>',
                address='123 Wholesale St, Business City, BC 12345',
                tax_id='TAX123456789'
            )
            db.session.add(supplier)
        
        # Create sample products
        electronics_cat = Category.query.filter_by(name='Electronics').first()
        clothing_cat = Category.query.filter_by(name='Clothing').first()
        food_cat = Category.query.filter_by(name='Food & Beverages').first()
        
        sample_products = [
            {
                'name': 'Wireless Bluetooth Headphones',
                'barcode': '1234567890123',
                'description': 'High-quality wireless headphones with noise cancellation',
                'cost_price': 45.00,
                'selling_price': 79.99,
                'quantity': 25,
                'category_id': electronics_cat.id if electronics_cat else 1
            },
            {
                'name': 'Cotton T-Shirt',
                'barcode': '2345678901234',
                'description': '100% cotton comfortable t-shirt',
                'cost_price': 8.00,
                'selling_price': 19.99,
                'quantity': 50,
                'category_id': clothing_cat.id if clothing_cat else 2
            },
            {
                'name': 'Premium Coffee Beans',
                'barcode': '3456789012345',
                'description': 'Organic premium coffee beans - 1lb bag',
                'cost_price': 12.00,
                'selling_price': 24.99,
                'quantity': 8,  # Low stock item
                'category_id': food_cat.id if food_cat else 3
            },
            {
                'name': 'Smartphone Case',
                'barcode': '4567890123456',
                'description': 'Protective case for smartphones',
                'cost_price': 5.00,
                'selling_price': 14.99,
                'quantity': 30,
                'category_id': electronics_cat.id if electronics_cat else 1
            },
            {
                'name': 'Energy Drink',
                'barcode': '5678901234567',
                'description': 'Energy drink 16oz can',
                'cost_price': 1.50,
                'selling_price': 3.99,
                'quantity': 5,  # Low stock item
                'category_id': food_cat.id if food_cat else 3
            }
        ]
        
        for product_data in sample_products:
            product = Product.query.filter_by(barcode=product_data['barcode']).first()
            if not product:
                print(f"Creating product: {product_data['name']}")
                product = Product(**product_data)
                db.session.add(product)
        
        # Create system settings
        settings_data = [
            {'key': 'shop_name', 'value': 'My Retail Store', 'description': 'Name of the retail shop'},
            {'key': 'shop_address', 'value': '123 Main St, City, State 12345', 'description': 'Shop address'},
            {'key': 'shop_phone', 'value': '******-0199', 'description': 'Shop phone number'},
            {'key': 'shop_email', 'value': '<EMAIL>', 'description': 'Shop email address'},
            {'key': 'tax_rate', 'value': '0.15', 'description': 'Default tax rate (15%)'},
            {'key': 'currency_symbol', 'value': '$', 'description': 'Currency symbol'},
            {'key': 'receipt_footer', 'value': 'Thank you for your business!', 'description': 'Receipt footer message'},
            {'key': 'low_stock_threshold', 'value': '10', 'description': 'Low stock alert threshold'},
        ]
        
        for setting_data in settings_data:
            setting = SystemSettings.query.filter_by(key=setting_data['key']).first()
            if not setting:
                print(f"Creating system setting: {setting_data['key']}")
                setting = SystemSettings(**setting_data)
                db.session.add(setting)
        
        # Commit all changes
        db.session.commit()
        print("Database initialization completed successfully!")
        
        # Print login credentials
        print("\n" + "="*50)
        print("DEFAULT LOGIN CREDENTIALS:")
        print("="*50)
        print("Administrator:")
        print("  Username: admin")
        print("  Password: admin123")
        print("\nCashier:")
        print("  Username: cashier")
        print("  Password: cashier123")
        print("\n⚠️  IMPORTANT: Change these passwords in production!")
        print("="*50)

if __name__ == '__main__':
    init_database()
