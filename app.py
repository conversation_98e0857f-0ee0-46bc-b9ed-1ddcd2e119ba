from flask import Flask
import os

# Try to import optional dependencies
try:
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import <PERSON>gin<PERSON>anager
    from flask_migrate import Migrate
    from flask_babel import Babel
    from dotenv import load_dotenv
    EXTENSIONS_AVAILABLE = True
except ImportError as e:
    print(f"Some extensions not available: {e}")
    EXTENSIONS_AVAILABLE = False

if EXTENSIONS_AVAILABLE:
    # Load environment variables
    load_dotenv()

    # Initialize extensions
    db = SQLAlchemy()
    migrate = Migrate()
    babel = Babel()
else:
    db = None
    login_manager = None
    migrate = None
    babel = None

def create_app(config_name='development'):
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'retail-pos-secret-key-2024')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///retail_pos.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['UPLOAD_FOLDER'] = 'uploads'
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

    if EXTENSIONS_AVAILABLE:
        # Babel configuration
        app.config['LANGUAGES'] = {
            'en': 'English',
            'ar': 'العربية'
        }
        app.config['BABEL_DEFAULT_LOCALE'] = 'en'
        app.config['BABEL_DEFAULT_TIMEZONE'] = 'UTC'

        # Initialize extensions with app
        db.init_app(app)
        login_manager.init_app(app)
        migrate.init_app(app, db)
        babel.init_app(app)
    
    if EXTENSIONS_AVAILABLE and login_manager:
        # Login manager configuration
        login_manager.login_view = 'auth.login'
        login_manager.login_message = 'Please log in to access this page.'
        login_manager.login_message_category = 'info'
    
    # Create upload directory
    upload_dir = os.path.join(app.instance_path, 'uploads')
    os.makedirs(upload_dir, exist_ok=True)
    
    # Register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.inventory import bp as inventory_bp
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    
    from app.sales import bp as sales_bp
    app.register_blueprint(sales_bp, url_prefix='/sales')
    
    from app.purchases import bp as purchases_bp
    app.register_blueprint(purchases_bp, url_prefix='/purchases')
    
    from app.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    if EXTENSIONS_AVAILABLE:
        # Import models to ensure they are registered with SQLAlchemy
        from app import models

        # User loader for Flask-Login
        @login_manager.user_loader
        def load_user(user_id):
            from app.models import User
            return User.query.get(int(user_id))

    return app

if __name__ == '__main__':
    app = create_app()
    if EXTENSIONS_AVAILABLE and db:
        with app.app_context():
            db.create_all()
    print("Starting Retail POS System...")
    print("Access the application at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=9000)
