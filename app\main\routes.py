from flask import render_template, redirect, url_for, flash, request, jsonify
from app.main import bp
from datetime import datetime, timedelta

try:
    from flask_login import login_required, current_user
    from app.models import Product, Sale, Purchase, User, Category, SaleItem
    from app import db
    from sqlalchemy import func
    EXTENSIONS_AVAILABLE = True
except ImportError:
    EXTENSIONS_AVAILABLE = False
    # Create dummy decorators for when extensions aren't available
    def login_required(f):
        return f
    current_user = None

@bp.route('/')
@bp.route('/dashboard')
@login_required
def dashboard():
    if not EXTENSIONS_AVAILABLE:
        return render_template('dashboard.html',
                             today_revenue=0,
                             today_receipts=0,
                             monthly_revenue=0,
                             low_stock_products=[],
                             top_products=[],
                             recent_sales=[])

    # Get today's date
    today = datetime.utcnow().date()

    # Calculate dashboard metrics
    today_sales = Sale.query.filter(func.date(Sale.created_at) == today).all()
    today_revenue = sum(sale.total_amount for sale in today_sales)
    today_receipts = len(today_sales)

    # Monthly metrics
    month_start = today.replace(day=1)
    monthly_sales = Sale.query.filter(Sale.created_at >= month_start).all()
    monthly_revenue = sum(sale.total_amount for sale in monthly_sales)

    # Low stock products
    low_stock_products = Product.query.filter(Product.quantity <= Product.min_quantity).all()

    # Top selling products (this month)
    top_products = db.session.query(
        Product.name,
        func.sum(SaleItem.quantity).label('total_sold'),
        func.sum(SaleItem.total_price).label('total_revenue')
    ).join(SaleItem).join(Sale).filter(
        Sale.created_at >= month_start
    ).group_by(Product.id).order_by(
        func.sum(SaleItem.quantity).desc()
    ).limit(5).all()

    # Recent sales
    recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(10).all()

    return render_template('dashboard.html',
                         today_revenue=today_revenue,
                         today_receipts=today_receipts,
                         monthly_revenue=monthly_revenue,
                         low_stock_products=low_stock_products,
                         top_products=top_products,
                         recent_sales=recent_sales)

@bp.route('/settings')
@login_required
def settings():
    if EXTENSIONS_AVAILABLE and current_user and not current_user.has_permission('manage_users'):
        flash('You do not have permission to access settings.', 'error')
        return redirect(url_for('main.dashboard'))

    return render_template('settings.html')

@bp.route('/test')
def test():
    return '''
    <h1>Retail POS System - Test Page</h1>
    <p>Flask application is running successfully!</p>
    <p>Extensions available: {}</p>
    <ul>
        <li><a href="/">Dashboard</a></li>
        <li><a href="/auth/login">Login</a></li>
        <li><a href="/settings">Settings</a></li>
    </ul>
    '''.format(EXTENSIONS_AVAILABLE)
