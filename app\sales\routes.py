from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.sales import bp
from app.models import Sale, SaleItem, Product
from app import db

@bp.route('/')
@login_required
def index():
    if not current_user.has_permission('make_sales'):
        flash('You do not have permission to access sales.', 'error')
        return redirect(url_for('main.dashboard'))
    
    return render_template('sales/pos.html')

@bp.route('/pos')
@login_required
def pos():
    if not current_user.has_permission('make_sales'):
        flash('You do not have permission to make sales.', 'error')
        return redirect(url_for('main.dashboard'))
    
    return render_template('sales/pos.html')

@bp.route('/history')
@login_required
def history():
    sales = Sale.query.order_by(Sale.created_at.desc()).all()
    return render_template('sales/history.html', sales=sales)
