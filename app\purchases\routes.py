from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.purchases import bp
from app.models import Purchase, PurchaseItem, Supplier, Product
from app import db

@bp.route('/')
@login_required
def index():
    if not current_user.has_permission('manage_purchases'):
        flash('You do not have permission to access purchases.', 'error')
        return redirect(url_for('main.dashboard'))
    
    purchases = Purchase.query.order_by(Purchase.created_at.desc()).all()
    return render_template('purchases/index.html', purchases=purchases)

@bp.route('/suppliers')
@login_required
def suppliers():
    if not current_user.has_permission('manage_purchases'):
        flash('You do not have permission to access suppliers.', 'error')
        return redirect(url_for('main.dashboard'))
    
    suppliers = Supplier.query.all()
    return render_template('purchases/suppliers.html', suppliers=suppliers)
