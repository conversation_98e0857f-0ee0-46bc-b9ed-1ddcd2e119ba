{% extends "base.html" %}

{% block title %}Dashboard - Retail POS System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
            <small class="text-muted">Welcome back, {{ current_user.full_name }}!</small>
        </h1>
    </div>
</div>

<!-- Key Metrics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">${{ "%.2f"|format(today_revenue) }}</h4>
                        <p class="card-text">Today's Sales</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ today_receipts }}</h4>
                        <p class="card-text">Today's Receipts</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">${{ "%.2f"|format(monthly_revenue) }}</h4>
                        <p class="card-text">Monthly Sales</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ low_stock_products|length }}</h4>
                        <p class="card-text">Low Stock Items</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                {% if current_user.has_permission('make_sales') %}
                <a href="{{ url_for('sales.pos') }}" class="btn btn-primary btn-lg w-100 mb-2">
                    <i class="fas fa-cash-register"></i> Start Sale
                </a>
                {% endif %}
                
                {% if current_user.has_permission('manage_products') %}
                <a href="{{ url_for('inventory.products') }}" class="btn btn-secondary w-100 mb-2">
                    <i class="fas fa-plus"></i> Add Product
                </a>
                {% endif %}
                
                {% if current_user.has_permission('view_reports') %}
                <a href="{{ url_for('reports.sales_report') }}" class="btn btn-info w-100">
                    <i class="fas fa-chart-bar"></i> View Reports
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Top Selling Products -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-star"></i> Top Selling Products</h5>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="list-group list-group-flush">
                        {% for product in top_products %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ product.name }}</strong><br>
                                <small class="text-muted">Sold: {{ product.total_sold }}</small>
                            </div>
                            <span class="badge bg-primary rounded-pill">${{ "%.2f"|format(product.total_revenue) }}</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No sales data available yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Low Stock Alert -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alert</h5>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                    <div class="list-group list-group-flush">
                        {% for product in low_stock_products[:5] %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ product.name }}</strong><br>
                                <small class="text-muted">{{ product.category.name }}</small>
                            </div>
                            <span class="badge bg-warning rounded-pill">{{ product.quantity }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% if low_stock_products|length > 5 %}
                    <div class="mt-2">
                        <a href="{{ url_for('inventory.low_stock') }}" class="btn btn-sm btn-outline-warning">
                            View All ({{ low_stock_products|length }})
                        </a>
                    </div>
                    {% endif %}
                {% else %}
                    <p class="text-success">All products are well stocked!</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Sales -->
{% if recent_sales %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> Recent Sales</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Receipt #</th>
                                <th>Date</th>
                                <th>Total</th>
                                <th>Payment Method</th>
                                <th>Cashier</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td>{{ sale.receipt_number }}</td>
                                <td>{{ sale.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>${{ "%.2f"|format(sale.total_amount) }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ sale.payment_method.title() }}</span>
                                </td>
                                <td>{{ sale.user.full_name }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
