from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.reports import bp
from app.models import Sale, Purchase, Expense, Product, SaleItem
from app import db

@bp.route('/')
@login_required
def index():
    if not current_user.has_permission('view_reports'):
        flash('You do not have permission to access reports.', 'error')
        return redirect(url_for('main.dashboard'))
    
    return render_template('reports/index.html')

@bp.route('/sales')
@login_required
def sales_report():
    if not current_user.has_permission('view_reports'):
        flash('You do not have permission to view reports.', 'error')
        return redirect(url_for('main.dashboard'))
    
    return render_template('reports/sales.html')

@bp.route('/profit-loss')
@login_required
def profit_loss():
    if not current_user.has_permission('view_reports'):
        flash('You do not have permission to view reports.', 'error')
        return redirect(url_for('main.dashboard'))
    
    return render_template('reports/profit_loss.html')
