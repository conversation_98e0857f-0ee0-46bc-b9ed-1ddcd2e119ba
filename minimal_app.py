from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def home():
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Retail POS System</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h2><i class="fas fa-cash-register"></i> Retail POS System</h2>
                        </div>
                        <div class="card-body">
                            <h4>Welcome to the Retail POS System!</h4>
                            <p>This is a comprehensive point-of-sale system for small retail shops.</p>
                            
                            <h5>Features:</h5>
                            <ul>
                                <li>Sales Management with barcode scanning</li>
                                <li>Inventory Management</li>
                                <li>Purchase & Supplier Management</li>
                                <li>Dashboard with analytics</li>
                                <li>User Management with role-based access</li>
                                <li>Comprehensive Reporting</li>
                                <li>Multi-language support (English/Arabic)</li>
                            </ul>
                            
                            <div class="alert alert-info">
                                <strong>Next Steps:</strong>
                                <ol>
                                    <li>Install required dependencies: <code>pip install -r requirements.txt</code></li>
                                    <li>Initialize the database: <code>python init_db.py</code></li>
                                    <li>Run the full application: <code>python app.py</code></li>
                                </ol>
                            </div>
                            
                            <div class="alert alert-warning">
                                <strong>Default Login Credentials:</strong><br>
                                <strong>Admin:</strong> username: admin, password: admin123<br>
                                <strong>Cashier:</strong> username: cashier, password: cashier123
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ''')

@app.route('/test')
def test():
    return '<h1>Test route working!</h1><p><a href="/">Back to home</a></p>'

if __name__ == '__main__':
    print("Starting Retail POS System (Minimal Version)...")
    print("Access the application at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
