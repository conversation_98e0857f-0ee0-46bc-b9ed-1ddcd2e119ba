#!/usr/bin/env python3
"""
Setup script for Retail POS System
This script installs dependencies and initializes the database
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"🔄 {description}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Return code: {e.returncode}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ is required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def create_virtual_environment():
    """Create a virtual environment"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("📁 Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def activate_and_install():
    """Install requirements in virtual environment"""
    # Check if we're on Windows or Unix-like system
    if os.name == 'nt':  # Windows
        pip_command = "venv\\Scripts\\pip install -r requirements.txt"
        python_command = "venv\\Scripts\\python"
    else:  # Unix-like (Linux, macOS)
        pip_command = "venv/bin/pip install -r requirements.txt"
        python_command = "venv/bin/python"
    
    # Install requirements
    if not run_command(pip_command, "Installing Python dependencies"):
        return False
    
    # Initialize database
    if not run_command(f"{python_command} init_db.py", "Initializing database"):
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "uploads",
        "backups",
        "logs",
        "instance"
    ]
    
    print("\n📁 Creating necessary directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def main():
    """Main setup function"""
    print("🚀 Retail POS System Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Create virtual environment
    if not create_virtual_environment():
        print("❌ Failed to create virtual environment")
        sys.exit(1)
    
    # Install dependencies and initialize database
    if not activate_and_install():
        print("❌ Failed to install dependencies or initialize database")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print("\n📋 NEXT STEPS:")
    print("1. Activate the virtual environment:")
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\activate")
        print("\n2. Run the application:")
        print("   python app.py")
    else:  # Unix-like
        print("   source venv/bin/activate")
        print("\n2. Run the application:")
        print("   python app.py")
    
    print("\n3. Open your browser and go to: http://localhost:5000")
    
    print("\n🔐 DEFAULT LOGIN CREDENTIALS:")
    print("   Administrator: admin / admin123")
    print("   Cashier: cashier / cashier123")
    print("   ⚠️  Change these passwords in production!")
    
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
